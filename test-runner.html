<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cookie Bypass Security Test</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #00ff00;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .section {
            background: #2a2a2a;
            border: 1px solid #00ff00;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #00ff00;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            background: #1a1a1a;
            border: 1px solid #00ff00;
            color: #00ff00;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        button {
            background: #00ff00;
            color: #1a1a1a;
            border: none;
            padding: 12px 24px;
            border-radius: 3px;
            cursor: pointer;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #00cc00;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .output {
            background: #000;
            border: 1px solid #00ff00;
            padding: 15px;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 15px;
        }
        .warning {
            background: #ff4444;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        .success { color: #00ff00; }
        .error { color: #ff4444; }
        .info { color: #4444ff; }
        .warning-text { color: #ffaa00; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 Cookie Bypass Security Test</h1>
            <p>Security testing tool for analyzing cookie injection mechanisms</p>
        </div>

        <div class="warning">
            ⚠️ WARNING: This tool is for authorized security testing only!
            Only use on systems you own or have explicit permission to test.
        </div>

        <div class="section">
            <h2>🎯 Test Configuration</h2>
            <div class="input-group">
                <label for="tldrToken">TLDR Token (for API testing):</label>
                <input type="text" id="tldrToken" placeholder="Enter your test token here..." />
            </div>
            <div class="input-group">
                <label for="targetDomain">Target Domain (optional):</label>
                <input type="text" id="targetDomain" placeholder="e.g., example.com" />
            </div>
            <button onclick="runDomainTest()">🌐 Test Domain Fetch</button>
            <button onclick="runTokenExtraction()">🔑 Extract TLDR Tokens</button>
            <button onclick="runAutoBypass()">🤖 Auto Bypass (No Token Needed)</button>
            <button onclick="runFullTest()">🚀 Run Full Bypass Test</button>
            <button onclick="analyzeOnly()">🔬 Analyze Technique Only</button>
            <button onclick="clearOutput()">🗑️ Clear Output</button>
        </div>

        <div class="section">
            <h2>📊 Test Results</h2>
            <div id="output" class="output">Ready to run tests...\n\nClick a button above to start testing.</div>
        </div>

        <div class="section">
            <h2>📋 How It Works</h2>
            <p>This tool demonstrates the cookie bypass mechanism used by the extension:</p>
            <ol>
                <li><strong>Domain Validation:</strong> Fetches allowed domains from remote server</li>
                <li><strong>API Authentication:</strong> Uses hardcoded Bearer token to access cookie API</li>
                <li><strong>Cookie Extraction:</strong> Retrieves session cookies and metadata</li>
                <li><strong>Header Injection:</strong> Uses declarativeNetRequest to modify HTTP headers</li>
                <li><strong>CSRF Bypass:</strong> Automatically injects CSRF tokens</li>
            </ol>
        </div>
    </div>

    <script src="cookie-bypass-demo.js"></script>
    <script>
        let output = document.getElementById('output');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            const prefix = {
                'success': '✅',
                'error': '❌',
                'warning': '⚠️',
                'info': 'ℹ️'
            }[type] || 'ℹ️';

            output.innerHTML += `<span class="${className}">[${timestamp}] ${prefix} ${message}</span>\n`;
            output.scrollTop = output.scrollHeight;
        }

        function clearOutput() {
            output.innerHTML = 'Output cleared.\n\n';
        }

        async function runDomainTest() {
            log('Starting domain fetch test...', 'info');

            try {
                const demo = new CookieBypassDemo();
                const domains = await demo.fetchAllowedDomains();

                log(`Successfully fetched ${domains.length} allowed domains:`, 'success');
                domains.forEach(domain => {
                    log(`  - ${domain}`, 'info');
                });

            } catch (error) {
                log(`Domain test failed: ${error.message}`, 'error');
            }
        }

        async function runTokenExtraction() {
            log('Starting TLDR token extraction...', 'info');

            try {
                const demo = new CookieBypassDemo();
                const tokens = await demo.extractTldrTokens();

                if (tokens.length > 0) {
                    log(`Successfully extracted ${tokens.length} TLDR tokens:`, 'success');
                    tokens.forEach((tokenInfo, index) => {
                        log(`  ${index + 1}. ${tokenInfo.buttonText}: ${tokenInfo.token.substring(0, 30)}...`, 'info');
                    });

                    // Auto-fill the first token
                    if (tokens.length > 0) {
                        document.getElementById('tldrToken').value = tokens[0].token;
                        log('First token auto-filled in the input field', 'success');
                    }
                } else {
                    log('No TLDR tokens found on the login page', 'warning');
                }

            } catch (error) {
                log(`Token extraction failed: ${error.message}`, 'error');
            }
        }

        async function runAutoBypass() {
            log('Starting automatic bypass (no token required)...', 'info');

            try {
                const demo = new CookieBypassDemo();
                const result = await demo.demonstrateBypassWithAutoTokens();

                if (result.success) {
                    log('🎉 Automatic bypass completed successfully!', 'success');
                    log(`Token used: ${result.tokenUsed.buttonText}`, 'info');
                    log(`Target URL: ${result.targetUrl}`, 'info');
                    log(`Domain: ${result.domain}`, 'info');
                    log(`Rule ID: ${result.injectionRule.id}`, 'info');
                    log(`Headers to inject: ${result.injectionRule.action.requestHeaders.length}`, 'info');
                } else {
                    log(`Auto bypass failed: ${result.error}`, 'error');
                }

            } catch (error) {
                log(`Auto bypass error: ${error.message}`, 'error');
            }
        }

        async function runFullTest() {
            const token = document.getElementById('tldrToken').value.trim();

            if (!token) {
                log('Please enter a TLDR token to run the full test', 'warning');
                return;
            }

            log('Starting full bypass test...', 'info');

            try {
                const demo = new CookieBypassDemo();
                const result = await demo.demonstrateBypass(token);

                if (result.success) {
                    log('Full bypass test completed successfully!', 'success');
                    log(`Target URL: ${result.targetUrl}`, 'info');
                    log(`Domain: ${result.domain}`, 'info');
                    log(`Rule ID: ${result.injectionRule.id}`, 'info');
                    log(`Headers to inject: ${result.injectionRule.action.requestHeaders.length}`, 'info');
                } else {
                    log(`Full test failed: ${result.error}`, 'error');
                }

            } catch (error) {
                log(`Full test error: ${error.message}`, 'error');
            }
        }

        function analyzeOnly() {
            log('Running technique analysis...', 'info');

            const demo = new CookieBypassDemo();

            // Capture console output
            const originalLog = console.log;
            console.log = function(...args) {
                log(args.join(' '), 'info');
            };

            demo.analyzeBypassTechnique();

            // Restore console.log
            console.log = originalLog;

            log('Analysis complete!', 'success');
        }

        // Override console methods to capture output
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;

        console.log = function(...args) {
            const message = args.join(' ');
            if (message.includes('✅')) {
                log(message, 'success');
            } else if (message.includes('❌')) {
                log(message, 'error');
            } else if (message.includes('⚠️')) {
                log(message, 'warning');
            } else {
                log(message, 'info');
            }
            originalConsoleLog.apply(console, args);
        };

        console.error = function(...args) {
            log(args.join(' '), 'error');
            originalConsoleError.apply(console, args);
        };

        // Initial load message
        log('Cookie Bypass Security Test Tool loaded', 'success');
        log('Ready to run security tests', 'info');
    </script>
</body>
</html>
