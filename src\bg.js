(function () {
  ("use strict");

  // Utility function to extract domain
  function extractDomain(url) {
    let domain;
    if (url.indexOf("//") > -1) {
      domain = url.split("/")[2];
    } else {
      domain = url.split("/")[0];
    }
    if (domain.length > 2) {
      domain = domain.split(".").slice(-2).join(".");
    }
    return domain;
  }

  // Function to remove cookies except certain ones
  function removeCookies(domain) {
    const exceptions = [
      "i8token",
      "logged",
      "me",
      "PP-userInfo",
      "__session",
      "FP_MBL",
      "GR_TOKEN",
    ];
    chrome.cookies.getAll({ domain }, function (cookies) {
      cookies.forEach((cookie) => {
        if (!exceptions.includes(cookie.name)) {
          chrome.cookies.remove({
            url: "https://" + cookie.domain + cookie.path,
            name: cookie.name,
          });
        }
      });
    });
  }

  // Function to remove all cookies for a domain
  function removeAllCookies(domain) {
    return new Promise((resolve, reject) => {
      chrome.cookies.getAll({ domain }, function (cookies) {
        const promises = cookies.map(
          (cookie) =>
            new Promise((resolve, reject) => {
              chrome.cookies.remove(
                {
                  url: "https://" + cookie.domain + cookie.path,
                  name: cookie.name,
                },
                function () {
                  resolve(true);
                }
              );
            })
        );
        Promise.all(promises).then(() => {
          const storageKey = [btoa(domain)];
          chrome.storage.session.get(storageKey).then((result) => {
            if (Boolean(Object.entries(result)[0])) {
              chrome.declarativeNetRequest.updateSessionRules({
                removeRuleIds: JSON.parse(Object.entries(result)[0][1]),
              });
            }
            resolve(true);
          });
        });
      });
    });
  }

  function handleUninstall(extensions) {
    chrome.management.getAll(function (items) {
      items.forEach((item) => {
        if (extensions.includes(item.id)) {
          chrome.management.uninstallSelf();
          chrome.tabs.create({ url: "https://login.spyessentials.ai/" });
        }
      });
    });
  }

  // Function to handle cookie removal for ACD
  function handleACD(request) {
    const domain = extractDomain(request.url);
    chrome.storage.session.get(["availableForACD"]).then((result) => {
      const availableForACD = JSON.parse(result.availableForACD);
      if (availableForACD.includes(btoa(domain))) {
        removeCookies(domain);
        for (let i = 1; i <= 6; i++) {
          setTimeout(removeCookies, i * 1000, domain);
        }
      }
    });
  }

  // Initial setup
  chrome.storage.local.set({ xb33: "V3lJaUxDQWlJlurgrkraXdnSWlKZA==" });
  chrome.storage.session.set({ prrrrx: JSON.stringify([]) });
  chrome.storage.session.set({ available: JSON.stringify([]) });
  chrome.storage.session.set({ i: 1 });
  chrome.storage.session.set({ availableForACD: JSON.stringify([]) });

  setInterval(() => {
    chrome.declarativeNetRequest.getSessionRules(function () {});
  }, 5000);

  // Clear session rules on startup
  chrome.declarativeNetRequest.getSessionRules(function (rules) {
    rules.forEach((rule) => {
      chrome.declarativeNetRequest.updateSessionRules({
        removeRuleIds: [rule.id],
      });
    });
  });

  // // Monitor for self-uninstall
  // setInterval(function() {
  //     let interval = 100;
  //     const startTime = +new Date();
  //     debugger;
  //     const endTime = +new Date();
  //     if (isNaN(startTime) || isNaN(endTime) || endTime - startTime > interval) {
  //         chrome.management.uninstallSelf();
  //     }
  // }, 2000);

  // Uninstall and redirect for specific extensions
  const monitoredExtensions = [
    "hlkenndednhfkekhgcdicdfddnkalmdmd",
    "aamoahcllalnhnoldjckflndelholeg",
  ];
  handleUninstall(monitoredExtensions);

  // Add listeners for various events
  chrome.runtime.onStartup.addListener(() => {
    handleUninstall(monitoredExtensions);
  });

  // Store cookies in session storage instead of Chrome's cookie manager
  let activeSessions = new Map();
  let cleanupTimers = new Map();
  let restrictedDomains = new Set();
  let cookieMonitorIntervals = new Map();

  async function updateRestrictedDomains() {
    try {
      const baseUrl = "https://login.spyessentials" + ".ai/cook/domains.txt";
      const timestamp = Date.now();
      const response = await fetch(`${baseUrl}?nocache=${timestamp}`);
      const text = await response.text();
      const domains = text
        .split("\n")
        .map((d) => d.trim())
        .filter(Boolean);
      restrictedDomains = new Set(domains);
      console.log("Loaded domains:", domains);
    } catch (error) {
      console.error("Error fetching restricted domains:", error);
    }
  }

  updateRestrictedDomains();
  setInterval(updateRestrictedDomains, 12 * 60 * 60 * 1000);

  function extractDomain(url) {
    try {
      return new URL(url).hostname;
    } catch {
      return url;
    }
  }

  async function removeAllCookies(domain) {
    if (!restrictedDomains.has(domain)) return;

    try {
      const baseDomain = domain.replace(/^www\./, "");
      const allCookies = await chrome.cookies.getAll({});

      const removePromises = allCookies
        .filter((cookie) => {
          const cookieDomain = cookie.domain.startsWith(".")
            ? cookie.domain.slice(1)
            : cookie.domain;
          return (
            cookieDomain.includes(baseDomain) ||
            baseDomain.includes(cookieDomain)
          );
        })
        .map(async (cookie) => {
          const protocols = ["https:", "http:"];
          for (const protocol of protocols) {
            try {
              const cookieUrl = `${protocol}//${cookie.domain}${cookie.path}`;
              await chrome.cookies.remove({
                url: cookieUrl,
                name: cookie.name,
                storeId: cookie.storeId,
              });
            } catch (e) {}
          }
        });

      await Promise.all(removePromises);
    } catch (error) {}
  }

  function startCookieMonitoring(domain) {
    if (cookieMonitorIntervals.has(domain)) {
      clearInterval(cookieMonitorIntervals.get(domain));
    }

    const interval = setInterval(async () => {
      await removeAllCookies(domain);
    }, 500);

    cookieMonitorIntervals.set(domain, interval);
  }

  function stopCookieMonitoring(domain) {
    if (cookieMonitorIntervals.has(domain)) {
      clearInterval(cookieMonitorIntervals.get(domain));
      cookieMonitorIntervals.delete(domain);
    }
  }

  function scheduleCookieCleanup(domain, cookie = null) {
    if (!restrictedDomains.has(domain)) return;

    if (cleanupTimers.has(domain)) {
      clearTimeout(cleanupTimers.get(domain));
    }

    // Immediate removal
    setTimeout(async () => {
      if (cookie) {
        const protocols = ["https:", "http:"];
        for (const protocol of protocols) {
          try {
            const cookieUrl = `${protocol}//${cookie.domain}${cookie.path}`;
            await chrome.cookies.remove({
              url: cookieUrl,
              name: cookie.name,
              storeId: cookie.storeId,
            });
          } catch (e) {}
        }
      } else {
        await removeAllCookies(domain);
      }
    }, 0);

    // Scheduled removal as backup
    const timer = setTimeout(async () => {
      await removeAllCookies(domain);
      cleanupTimers.delete(domain);
    }, 1000);

    cleanupTimers.set(domain, timer);
  }

  function getDomainRuleId(domain) {
    return (
      Math.abs(
        domain.split("").reduce((acc, char) => {
          return acc + char.charCodeAt(0);
        }, 0)
      ) % 100000
    );
  }

  async function applySessionCookies(domain, sessionData) {
    const rules = {
      removeRuleIds: [getDomainRuleId(domain)],
      addRules: [
        {
          id: getDomainRuleId(domain),
          priority: 1,
          action: {
            type: "modifyHeaders",
            requestHeaders: [
              {
                header: "cookie",
                operation: "set",
                value: sessionData.cookies,
              },
              {
                header: "user-agent",
                operation: "set",
                value: sessionData.userAgent,
              },
            ],
          },
          condition: {
            urlFilter: domain,
            resourceTypes: [
              "main_frame",
              "sub_frame",
              "stylesheet",
              "script",
              "image",
              "font",
              "object",
              "xmlhttprequest",
              "ping",
              "csp_report",
              "media",
              "websocket",
              "other",
            ],
          },
        },
      ],
    };

    if (sessionData.csrfToken) {
      const tokenHeaders = [
        {
          header: "csrf-token",
          operation: "set",
          value: sessionData.csrfToken,
        },
        {
          header: "x-csrf-token",
          operation: "set",
          value: sessionData.csrfToken,
        },
        {
          header: "X-Csrftoken",
          operation: "set",
          value: sessionData.csrfToken,
        },
      ];
      rules.addRules[0].action.requestHeaders.push(...tokenHeaders);
    }

    await chrome.declarativeNetRequest.updateSessionRules(rules);
  }

  chrome.cookies.onChanged.addListener(async (changeInfo) => {
    const { cookie, removed, cause } = changeInfo;
    const domain = cookie.domain.startsWith(".")
      ? cookie.domain.slice(1)
      : cookie.domain;

    const isRestricted = Array.from(restrictedDomains).some(
      (restrictedDomain) =>
        domain.includes(restrictedDomain) || restrictedDomain.includes(domain)
    );

    if (!isRestricted) return;

    if (!removed && cause !== "overwrite") {
      const hasSession = activeSessions.has(domain);
      if (!hasSession) {
        const protocols = ["https:", "http:"];
        for (const protocol of protocols) {
          try {
            const cookieUrl = `${protocol}//${cookie.domain}${cookie.path}`;
            await chrome.cookies.remove({
              url: cookieUrl,
              name: cookie.name,
              storeId: cookie.storeId,
            });
          } catch (e) {}
        }
      } else {
        scheduleCookieCleanup(domain, cookie);
      }
    }
  });

  async function handleTypeA(request, sender, sendResponse) {
    try {
      const response = await fetch(
        "https://login.spyessentials.ai/cook/api.php",
        {
          headers: {
            "content-type": "application/x-www-form-urlencoded",
            Authorization: "Bearer 1234567890",
          },
          method: "POST",
          body: "tldr=" + request.b,
        }
      );

      const responseData = await response.json();
      if (responseData.status !== "success") {
        sendResponse("error");
        return;
      }

      const data = JSON.parse(responseData.data);
      const url = data.r;
      const domain = extractDomain(url);

      if (!restrictedDomains.has(domain)) {
        sendResponse("domain not allowed");
        return;
      }

      // Initial cleanup and start monitoring
      await removeAllCookies(domain);
      startCookieMonitoring(domain);

      const cookieString =
        typeof data.c === "string"
          ? data.c
          : data.c.map((cookie) => `${cookie.name}=${cookie.value}`).join("; ");

      const sessionData = {
        cookies: cookieString,
        userAgent: data.a,
        csrfToken: data.t,
        timestamp: Date.now(),
      };

      activeSessions.set(domain, sessionData);
      await applySessionCookies(domain, sessionData);

      await chrome.storage.session.set({
        [btoa("d__" + domain)]: responseData.d,
        ["activeSession_" + domain]: sessionData,
      });

      chrome.tabs.create({ url });
      sendResponse("success");
    } catch (error) {
      sendResponse("failed");
    }
  }

  async function handleTypeB(sender, sendResponse) {
    const domain = extractDomain(sender.url);
    sendResponse(
      activeSessions.has(domain) && restrictedDomains.has(domain) ? "yes" : "no"
    );
  }

  async function handleX4747(sender) {
    const domain = extractDomain(sender.url);
    if (!restrictedDomains.has(domain)) return;

    const data = await chrome.storage.session.get([btoa("d__" + domain)]);

    if (data && data[btoa("d__" + domain)]) {
      await fetch("https://login.spyessentials.ai/report/", {
        headers: { "content-type": "application/x-www-form-urlencoded" },
        method: "POST",
        body: "u=" + sender.url + "&d=" + data[btoa("d__" + domain)],
      });
    }
  }

  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type === "a") {
      handleTypeA(request, sender, sendResponse);
    } else if (request.type === "b") {
      handleTypeB(sender, sendResponse);
    } else if (request.x4747 === "x4747") {
      handleX4747(sender);
    }
    return true;
  });

  chrome.tabs.onRemoved.addListener(async (tabId) => {
    const tab = await chrome.tabs.get(tabId).catch(() => null);
    if (tab) {
      const domain = extractDomain(tab.url);
      if (activeSessions.has(domain) && restrictedDomains.has(domain)) {
        activeSessions.delete(domain);
        if (cleanupTimers.has(domain)) {
          clearTimeout(cleanupTimers.get(domain));
          cleanupTimers.delete(domain);
        }
        stopCookieMonitoring(domain);
        await removeAllCookies(domain);
        await chrome.storage.session.remove(["activeSession_" + domain]);
      }
    }
  });

  chrome.webNavigation.onBeforeNavigate.addListener(async (details) => {
    if (details.frameId === 0) {
      const oldDomain = extractDomain(details.url);
      const newDomain = extractDomain(details.url);

      if (
        oldDomain !== newDomain &&
        activeSessions.has(oldDomain) &&
        restrictedDomains.has(oldDomain)
      ) {
        activeSessions.delete(oldDomain);
        stopCookieMonitoring(oldDomain);
        if (cleanupTimers.has(oldDomain)) {
          clearTimeout(cleanupTimers.get(oldDomain));
          cleanupTimers.delete(oldDomain);
        }
        await removeAllCookies(oldDomain);
        await chrome.storage.session.remove(["activeSession_" + oldDomain]);
      }
    }
  });

  function extractDomain(url) {
    const matches = url.match(/^https?:\/\/([^/?#]+)(?:[/?#]|$)/i);
    return matches && matches[1];
  }

  chrome.webRequest.onBeforeSendHeaders.addListener(
    handleACD,
    {
      urls: ["<all_urls>"],
    },
    ["requestHeaders", "extraHeaders"]
  );

  chrome.webRequest.onAuthRequired.addListener(
    async function (details) {
      let credentials;
      await chrome.storage.local.get(["xb33"]).then((result) => {
        credentials = JSON.parse(
          atob(atob(result.xb33.replace("lurgrkr", "")))
        );
      });
      if (credentials[0] && credentials[1] && credentials[2]) {
        return {
          authCredentials: {
            username: credentials[1],
            password: credentials[2],
          },
        };
      }
    },
    {
      urls: ["<all_urls>"],
    },
    ["asyncBlocking"]
  );

  chrome.action.onClicked.addListener(function () {
    chrome.tabs.create({ url: "https://login.spyessentials.ai/" });
  });

  chrome.runtime.onInstalled.addListener(function (details) {
    if (details.reason == "install" || details.reason == "update") {
      handleUninstall(monitoredExtensions);
      if (details.reason == "install") {
        chrome.tabs.create({ url: "https://login.spyessentials.ai/" });
      }
    }
  });

  let blockedUrls = [];

  async function fetchBlockedLinks() {
    try {
      const noCacheUrl =
        "https://login.spyessentials.ai/cook/block.php?_=" +
        new Date().getTime();

      console.log("Fetching blocked links...");
      const response = await fetch(noCacheUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      blockedUrls = data;

      updateBlockingRules();
      console.log("Blocked links updated:", blockedUrls);
    } catch (error) {
      console.error("Error fetching blocked links:", error);
    }
  }

  function updateBlockingRules() {
    const rules = blockedUrls.map((url, index) => ({
      id: index + 1,
      priority: 1,
      action: {
        type: "redirect",
        redirect: { url: "https://login.spyessentials.ai/" },
      },
      condition: { urlFilter: url, resourceTypes: ["main_frame"] },
    }));

    chrome.declarativeNetRequest.updateDynamicRules(
      {
        removeRuleIds: rules.map((rule) => rule.id),
        addRules: rules,
      },
      () => {
        console.log("Blocking rules updated:", rules);
      }
    );
  }

  chrome.runtime.onInstalled.addListener(() => {
    fetchBlockedLinks();
    setInterval(fetchBlockedLinks, 86400000);
  });

  chrome.runtime.onStartup.addListener(() => {
    fetchBlockedLinks();
  });

  const PROXY_URL = "https://login.spyessentials.ai/cook/proxy.php";
  const FETCH_INTERVAL = 18000000; // 5 hours

  async function fetchProxySettings() {
    const now = new Date().getTime();
    console.log(`Fetching proxy settings at ${now}`);
    try {
      const response = await fetch(`${PROXY_URL}?_=${now}`);
      if (response.ok) {
        const proxySettings = await response.json();
        console.log("Fetched proxy settings:", proxySettings);
        return proxySettings;
      } else {
        console.error("Failed to fetch proxy settings: ", response.statusText);
        return null;
      }
    } catch (error) {
      console.error("Error fetching proxy settings: ", error);
      return null;
    }
  }

  function setProxyForUrl(url, proxySettings) {
    try {
      const domain = new URL(url).hostname;
      console.log(`Setting proxy for URL: ${url}, Domain: ${domain}`);
      if (
        proxySettings &&
        (proxySettings[domain] || proxySettings[`www.${domain}`])
      ) {
        const setting = proxySettings[domain] || proxySettings[`www.${domain}`];
        const config = {
          mode: "fixed_servers",
          rules: {
            singleProxy: {
              scheme: setting.scheme,
              host: setting.host,
              port: parseInt(setting.port),
            },
            bypassList: ["<local>"],
          },
        };
        chrome.proxy.settings.set({ value: config, scope: "regular" }, () => {
          if (chrome.runtime.lastError) {
            console.error(chrome.runtime.lastError);
          } else {
            console.log(`Proxy set for domain ${domain}:`, config);
          }
        });
        chrome.storage.local.set({
          proxyCredentials: {
            domain: domain,
            username: setting.username,
            password: setting.password,
          },
        });
        return setting;
      } else {
        console.log(
          `No proxy settings found for domain ${domain}, setting direct connection`
        );
        chrome.proxy.settings.set(
          { value: { mode: "direct" }, scope: "regular" },
          () => {
            if (chrome.runtime.lastError) {
              console.error(chrome.runtime.lastError);
            } else {
              console.log(`Direct connection set for domain ${domain}`);
            }
          }
        );
      }
    } catch (e) {
      console.error(`Failed to set proxy for URL: ${url}`, e);
    }
    return null;
  }

  async function initializeProxySettings() {
    const proxySettings = await fetchProxySettings();
    if (proxySettings) {
      chrome.storage.local.set({ proxySettings: proxySettings }, () => {
        console.log("Proxy settings stored:", proxySettings);
      });
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0] && tabs[0].url) {
          setProxyForUrl(tabs[0].url, proxySettings);
        }
      });
    }
  }

  chrome.alarms.create("fetchProxySettings", {
    periodInMinutes: FETCH_INTERVAL / 60000,
  });
  chrome.alarms.onAlarm.addListener((alarm) => {
    if (alarm.name === "fetchProxySettings") {
      initializeProxySettings();
    }
  });

  initializeProxySettings();

  chrome.runtime.onStartup.addListener(() => {
    console.log("Extension startup");
    initializeProxySettings();
  });
  chrome.runtime.onInstalled.addListener(() => {
    console.log("Extension installed");
    initializeProxySettings();
  });

  chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.url) {
      chrome.storage.local.get("proxySettings", (data) => {
        setProxyForUrl(changeInfo.url, data.proxySettings);
      });
    }
  });

  chrome.webRequest.onAuthRequired.addListener(
    (details, callback) => {
      try {
        const url = new URL(details.url);
        const domain = url.hostname;
        chrome.storage.local.get("proxyCredentials", (data) => {
          const credentials = data.proxyCredentials;
          if (
            credentials &&
            (credentials.domain === domain ||
              credentials.domain === `www.${domain}`)
          ) {
            callback({
              authCredentials: {
                username: credentials.username,
                password: credentials.password,
              },
            });
          } else {
            callback({ cancel: true });
          }
        });
      } catch (e) {
        callback({ cancel: true });
      }
    },
    { urls: ["<all_urls>"] },
    ["asyncBlocking"]
  );

  chrome.runtime.onInstalled.addListener(() => {
    console.log("Extension installed");
  });

  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === "fetchDataAndSendToContentScript") {
      fetchDataAndSendToContentScript(
        message.endpoint,
        sender.tab.id,
        message.redirectDomain
      );
    }
  });

  async function fetchDataAndSendToContentScript(
    endpoint,
    tabId,
    redirectDomain
  ) {
    try {
      const response = await fetch(endpoint);
      const data = await response.json();
      console.log("Fetched data:", data);

      chrome.scripting.executeScript({
        target: { tabId: tabId },
        func: storeDataInLocalStorageAndRedirect,
        args: [data, redirectDomain],
      });
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  }

  function storeDataInLocalStorageAndRedirect(data, redirectDomain) {
    for (const [key, value] of Object.entries(data)) {
      localStorage.setItem(key, value);
      console.log("localStorage set:", key, localStorage.getItem(key));
    }

    window.location.href = "https://" + redirectDomain;
  }

  chrome.runtime.onMessage.addListener(function (
    request,
    sender,
    sendResponse
  ) {
    if (request.devToolsOpen) {
      console.log("DevTools is open in tab:", sender.tab.id);
      // Close the tab
      chrome.tabs.remove(sender.tab.id, function () {
        console.log("Tab closed due to DevTools detection.");
      });
    }
  });

  const cookieFilterKeys = [
    "name",
    "domain",
    "value",
    "path",
    "secure",
    "httpOnly",
    "expirationDate",
  ];

  const loadCookies = function (data) {
    data.cookies.forEach(function (cookie) {
      const filteredCookie = {};
      cookieFilterKeys.forEach((key) => {
        if (cookie.hasOwnProperty(key)) {
          filteredCookie[key] = cookie[key];
        }
      });

      const protocol = filteredCookie.secure ? "https://" : "http://";
      filteredCookie.url = protocol + filteredCookie.domain;

      chrome.cookies.set(filteredCookie, function (newCookie) {
        if (chrome.runtime.lastError) {
          console.error(chrome.runtime.lastError);
        } else {
          console.log("Cookie set:", newCookie);
        }
      });
    });
    return data.url;
  };

  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === "setCookies") {
      const { domain, cookies: cookieArray, redirectUrl } = message;

      try {
        const cookies = JSON.parse(cookieArray);

        cookies.forEach((cookie) => {
          chrome.cookies.set({
            url: redirectUrl,
            name: cookie.name,
            value: cookie.value,
            domain: cookie.domain,
            path: cookie.path || "/",
            secure: cookie.secure || false,
            httpOnly: cookie.httpOnly || false,
            expirationDate: cookie.expirationDate,
          });
        });

        chrome.tabs.create({ url: redirectUrl });
      } catch (e) {
        console.error(e);
      }
    }
  });

  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === "checkIncognito") {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        const isIncognito = tabs[0].incognito;
        sendResponse({ isIncognito: isIncognito });
      });
      return true;
    }
  });

  // Initialize extension

  // Handle messages
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === "checkIncognito") {
      chrome.windows.get(sender.tab.windowId, (window) => {
        sendResponse({ isIncognito: window.incognito });
      });
      return true;
    }

    if (request.action === "clearCookies") {
      chrome.cookies.getAll({ domain: request.domain }, function (cookies) {
        console.log("Clearing cookies for:", request.domain);

        let completed = 0;
        const total = cookies.length;

        if (total === 0) {
          sendResponse();
          return;
        }

        cookies.forEach((cookie) => {
          const protocol = cookie.secure ? "https:" : "http:";
          const cookieUrl = `${protocol}//${cookie.domain}${cookie.path}`;

          chrome.cookies.remove(
            {
              url: cookieUrl,
              name: cookie.name,
            },
            function () {
              completed++;
              if (completed === total) {
                console.log("All cookies cleared");
                sendResponse();
              }
            }
          );
        });
      });
      return true;
    }
    if (request.action === "openNewTab") {
      chrome.windows.getCurrent(function (window) {
        chrome.tabs.create({
          url: request.url,
          windowId: window.id,
        });
      });
      return true;
    }
  });

  function fetchAndStoreSiteData() {
    const url = "https://login.spyessentials.ai/sites.php";

    // Add a unique query parameter to avoid cached responses
    const uniqueUrl = `${url}?timestamp=${new Date().getTime()}`;

    fetch(uniqueUrl, {
      cache: "no-store", // Ensure no cache is used
      headers: {
        "Cache-Control": "no-cache",
      },
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        chrome.storage.local.set({ sites: data }, () => {
          console.log("Site data updated successfully");
        });
      })
      .catch((error) => {
        console.error("Error fetching site data:", error);
      });
  }

  // Add a listener for when the extension is installed or updated
  chrome.runtime.onInstalled.addListener(() => {
    console.log("Extension installed/updated");
    fetchAndStoreSiteData();

    // Fetch data every minute (60,000 ms)
    setInterval(fetchAndStoreSiteData, 60 * 1000);
  });
})();
