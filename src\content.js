(function () {
  ("use strict");

  window.onload = function () {
    let currentUrl = location.href;
    if (currentUrl.indexOf("https://login.spyessentials.ai") > -1) {
      document
        .querySelectorAll(".cooky_btn#x41x544")
        .forEach(function (button) {
          if (button) {
            button.parentElement.parentElement.querySelector(
              "#x56x455"
            ).style.display = "none";
            button.parentElement.style.display = "block";
            button.addEventListener("click", function () {
              let tldr = this.getAttribute("tdlr");
              let buttonElement = this;
              if (tldr) {
                buttonElement.style.display = "none";
                buttonElement.parentElement.querySelector(
                  "#loader"
                ).style.display = "block";
                chrome.runtime.sendMessage(
                  {
                    type: "a",
                    b: tldr,
                  },
                  function (response) {
                    if (response == "failed") {
                      buttonElement.style.display = "block";
                      buttonElement.parentElement.querySelector(
                        "#loader"
                      ).style.display = "none";
                      document
                        .querySelector(".notification_wrapper.err")
                        .classList.add("show");
                      setTimeout(function () {
                        document
                          .querySelector(".notification_wrapper.err")
                          .classList.remove("show");
                      }, 10000);
                    }
                    if (response == "error") {
                      document
                        .querySelector(".notification_wrapper.err1")
                        .classList.add("show");
                      setTimeout(function () {
                        document
                          .querySelector(".notification_wrapper.err1")
                          .classList.remove("show");
                      }, 10000);
                    }
                  }
                );
              }
            });
          }
        });
    } else {
      chrome.runtime.sendMessage(
        {
          type: "b",
        },
        function (response) {
          if (response == "yes") {
            (function () {
              "use strict";

              let devToolsStatus = {
                isOpen: false,
                orientation: undefined,
              };

              let dispatchDevToolsEvent = (isOpen, orientation) => {
                window.dispatchEvent(
                  new CustomEvent("devtoolschange", {
                    detail: {
                      isOpen: isOpen,
                      orientation: orientation,
                    },
                  })
                );
              };

              let checkDevTools = ({ emitEvents: emitEvents = true } = {}) => {
                let thresholdWidth = window.outerWidth / 4;
                let thresholdHeight = window.outerHeight / 3;
                let isVertical =
                  window.outerWidth - window.innerWidth > thresholdWidth;
                let isHorizontal =
                  window.outerHeight - window.innerHeight > thresholdHeight;
                let orientation = isVertical ? "vertical" : "horizontal";
                if (
                  (isHorizontal && isVertical) ||
                  !(
                    (window.Firebug &&
                      window.Firebug.chrome &&
                      window.Firebug.chrome.isInitialized) ||
                    isVertical ||
                    isHorizontal
                  )
                ) {
                  if (false && emitEvents) {
                    dispatchDevToolsEvent(false, undefined);
                  }
                  devToolsStatus.isOpen = false;
                  devToolsStatus.orientation = undefined;
                } else {
                  if (
                    !(
                      (false && devToolsStatus.orientation === orientation) ||
                      !emitEvents
                    )
                  ) {
                    dispatchDevToolsEvent(true, orientation);
                  }
                  devToolsStatus.isOpen = true;
                  devToolsStatus.orientation = orientation;
                }
              };

              checkDevTools({
                emitEvents: false,
              });

              setInterval(checkDevTools, 50);

              if (typeof module !== "undefined" && module.exports) {
                module.exports = devToolsStatus;
              } else {
                window.devToolsStatus = devToolsStatus;
              }

              if (window.devToolsStatus.isOpen) {
                chrome.runtime.sendMessage({
                  x4747: "x4747",
                });
              } else {
                window.addEventListener("devtoolschange", (event) => {
                  if (event.detail.isOpen) {
                    chrome.runtime.sendMessage(
                      {
                        x4747: "x4747",
                      },
                      function () {
                        chrome.runtime.sendMessage({
                          x4747: "x4747",
                        });
                      }
                    );
                  }
                });
              }

              setInterval(function (interval) {
                if (isNaN(+interval)) {
                  interval = 100;
                }
                let startTime = +new Date();
                debugger;
                let endTime = +new Date();
                if (
                  isNaN(startTime) ||
                  isNaN(endTime) ||
                  endTime - startTime > interval
                ) {
                  chrome.runtime.sendMessage({
                    x4747: "x4747",
                  });
                }
              }, 7000);
            })();
          }
        }
      );
    }
  };

  const websites = [
    {
      domain: "elevenlabs.io",
      rdomain: "elevenlabs.io",
      referrerDomain: "login.spyessentials.ai",
      endpoint: "https://login.spyessentials.ai/tools/eleven.php",
    },
    {
      domain: "adspy.com",
      rdomain: "app.adspy.com",
      referrerDomain: "login.spyessentials.ai",
      endpoint: "https://login.spyessentials.ai/tools/adspy.php",
    },
    {
      domain: "shophunter.io",
      rdomain: "app.shophunter.io/explore/products",
      referrerDomain: "login.spyessentials.ai",
      endpoint: "https://login.spyessentials.ai/tools/shop.php",
    },
    {
      domain: "my.brain.fm",
      rdomain: "my.brain.fm",
      referrerDomain: "login.spyessentials.ai",
      endpoint: "https://login.spyessentials.ai/tools/brain.php",
    },
    {
      domain: "app.pinspy.com",
      rdomain: "app.pinspy.com",
      referrerDomain: "login.spyessentials.ai",
      endpoint: "https://login.spyessentials.ai/tools/pinspy.php",
    },
  ];

  function isFromSpyEssentials(referringDomain) {
    return referringDomain === "login.spyessentials.ai";
  }

  function processWebsite() {
    const referringURL = document.referrer;
    const referringDomain = referringURL.split("/")[2];

    for (const website of websites) {
      if (
        window.location.href.includes(website.domain) &&
        isFromSpyEssentials(referringDomain)
      ) {
        chrome.runtime.sendMessage({
          action: "fetchDataAndSendToContentScript",
          endpoint: website.endpoint,
          redirectDomain: website.rdomain,
        });
        return;
      }
    }
  }

  processWebsite();

  (async function () {
    const hostname = window.location.hostname;

    try {
      const url = `https://login.spyessentials.ai/cook/style.php?domain=${hostname}`;
      const response = await fetch(url);
      const css = await response.text();

      if (css.trim() !== "/* No custom styles available for this domain */") {
        const style = document.createElement("style");
        style.type = "text/css";
        style.innerHTML = css;
        document.head.appendChild(style);
      }
    } catch (error) {
      console.error("Failed to load styles:", error);
    }
  })();

  document.querySelectorAll(".user_clickedd").forEach((button) => {
    button.addEventListener("click", function () {
      const textareaId = this.getAttribute("data-textarea");
      const textarea = document.getElementById(textareaId);
      const cookieJSON = textarea.value;

      try {
        const dataUrl = textarea.getAttribute("data-url") || "";

        chrome.runtime.sendMessage({
          action: "setCookies",
          cookies: cookieJSON,
          domain: dataUrl,
          redirectUrl: dataUrl,
        });
      } catch (e) {
        console.error(e);
      }
    });
  });

  console.log("Content script loaded at:", window.location.href);

  // Function to log storage data
  function logStorageData() {
    chrome.storage.local.get(null, function (items) {
      console.log("Current storage data:", items);
    });
  }

  chrome.runtime.sendMessage({ action: "checkIncognito" }, function (response) {
    chrome.storage.local.get("sites", (result) => {
      const sites = result.sites;
      console.log("Sites data loaded:", sites);

      if (sites) {
        if (window.location.hostname.includes("login.spyessentials.ai")) {
          setupAllSitesButtons(sites, response.isIncognito);
        }
      }
    });
  });

  function setupAllSitesButtons(sites, isIncognito) {
    if (!isIncognito) {
      const warning = document.getElementById("incognitoWarning");
      if (warning) warning.style.display = "block";
      return;
    }

    const containers = document.querySelectorAll("[data-show-site]");
    containers.forEach((container) => {
      const showSite = container.getAttribute("data-show-site");
      console.log("Setting up buttons for site:", showSite);

      if (showSite && sites[showSite]) {
        const buttonContainer = container.querySelector("#accountButtons");
        if (buttonContainer) {
          setupSiteButtons(showSite, sites[showSite], buttonContainer);
        }
      }
    });
  }

  function setupSiteButtons(siteName, siteConfig, container) {
    container.innerHTML = "";

    if (siteConfig.type === "single") {
      const button = createLoginButton(
        siteConfig.displayName || `Login to ${siteName}`, // Use displayName if available
        () => handleLogin(siteConfig)
      );
      container.appendChild(button);
    } else if (siteConfig.type === "multiple") {
      Object.entries(siteConfig.accounts).forEach(
        ([accountId, accountConfig]) => {
          const button = createLoginButton(
            accountConfig.displayName || `Account ${accountId}`,
            () => handleLogin(accountConfig)
          );
          container.appendChild(button);
        }
      );
    }
  }

  function createLoginButton(text, onClick) {
    const button = document.createElement("button");
    button.className = "account-button";
    button.textContent = text;
    button.addEventListener("click", onClick);
    return button;
  }

  function extractDomain(url) {
    const a = document.createElement("a");
    a.href = url;
    return a.hostname;
  }

  // Replace your existing handleLogin function with this:
  async function handleLogin(config) {
    if (!config.loginUrl) {
      console.error("Login URL not found in configuration");
      return;
    }

    try {
      // Clear cookies first
      const domain = extractDomain(config.loginUrl);
      await clearCookies(domain);
      console.log("Cookies cleared for:", domain);

      // Store config for autoLogin
      await chrome.storage.local.set({
        pendingLogin: {
          config: config,
          timestamp: Date.now(),
        },
      });

      // Open in new tab instead of redirecting
      chrome.runtime.sendMessage({
        action: "openNewTab",
        url: config.loginUrl,
      });
    } catch (error) {
      console.error("Login process failed:", error);
    }
  }

  function clearCookies(domain) {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage(
        {
          action: "clearCookies",
          domain: domain,
        },
        resolve
      );
    });
  }

  // Check for pending login when page loads
  chrome.storage.local.get(["pendingLogin"], async (result) => {
    const pendingLogin = result.pendingLogin;

    if (pendingLogin && Date.now() - pendingLogin.timestamp < 30000) {
      chrome.storage.local.remove("pendingLogin");
      await autoLogin(pendingLogin.config);
    }
  });

  async function autoLogin(config) {
    let overlay;

    try {
      overlay = document.createElement("div");
      overlay.style.cssText =
        "position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: white; z-index: 1000;";
      document.body.appendChild(overlay);

      const usernameField = await getElementWithRetry(config.usernameSelector);
      const passwordField = await getElementWithRetry(config.passwordSelector);
      const loginButton = await getElementWithRetry(config.loginButtonSelector);

      usernameField.value = config.username;
      passwordField.value = config.password;

      [usernameField, passwordField].forEach((field) => {
        field.dispatchEvent(new Event("focus"));
        field.dispatchEvent(new Event("input"));
        field.dispatchEvent(new Event("change"));
        field.dispatchEvent(new Event("blur"));
      });

      setTimeout(() => {
        loginButton.click();

        let lastUrl = window.location.href;
        let loadCheckInterval = setInterval(() => {
          if (
            window.location.href !== lastUrl &&
            document.readyState === "complete"
          ) {
            console.log("Page fully loaded after login");
            clearInterval(loadCheckInterval);

            setTimeout(() => {
              if (overlay && overlay.parentNode) {
                overlay.remove();
                console.log("Overlay removed after successful login");
              }
            }, 2000);
          }

          setTimeout(() => {
            if (overlay && overlay.parentNode) {
              overlay.remove();
              console.log("Overlay removed due to timeout");
            }
            clearInterval(loadCheckInterval);
          }, 30000);
        }, 500);
      }, 1000);
    } catch (error) {
      console.error("Login failed:", error);
      if (overlay && overlay.parentNode) {
        overlay.remove();
      }
    }
  }

  function getElementWithRetry(selector, retries = 5, interval = 500) {
    return new Promise((resolve, reject) => {
      const attempt = () => {
        const element = document.querySelector(selector);
        if (element) {
          resolve(element);
        } else if (retries > 0) {
          console.log(
            `Element ${selector} not found, retrying... (${retries} attempts left)`
          );
          setTimeout(attempt, interval);
          retries--;
        } else {
          reject(`Element not found: ${selector}`);
        }
      };
      attempt();
    });
  }
})();
